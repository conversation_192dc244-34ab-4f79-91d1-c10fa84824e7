import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Lock, AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { getTransferInfo, downloadCompressedFile, downloadOriginalFile } from '../services/api';
import type { TransferInfo } from '../services/api';
import { formatFileSize } from '../utils/formatters';

export default function SharedTransfer() {
  const { transferId } = useParams<{ transferId: string }>();
  const [transferInfo, setTransferInfo] = useState<TransferInfo | null>(null);

  // Debug log to confirm new code is loading
  console.log('SharedTransfer component loaded with new three-button layout');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    if (transferId) {
      loadTransferInfo();
    }
  }, [transferId]);

  const loadTransferInfo = async () => {
    if (!transferId) return;

    try {
      setLoading(true);
      const info = await getTransferInfo(transferId);
      setTransferInfo(info);
      setShowPasswordInput(info.hasPassword);
    } catch (error: any) {
      console.error('Error loading transfer info:', error);
      if (error.response?.status === 404) {
        setError('Transfer not found');
      } else if (error.response?.status === 410) {
        setError('Transfer has expired');
      } else {
        setError('Failed to load transfer information');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (downloadType: 'compressed' | 'original' = 'original') => {
    if (!transferInfo) return;

    try {
      setDownloading(true);
      if (downloadType === 'original') {
        await downloadOriginalFile(transferInfo.transferId, transferInfo.originalName);
      } else {
        await downloadCompressedFile(transferInfo.transferId, transferInfo.originalName);
      }
    } catch (error) {
      console.error('Download error:', error);
      alert(`Failed to download ${downloadType} file`);
    } finally {
      setDownloading(false);
    }
  };

  const handleCopyDownloadLink = async () => {
    if (!transferInfo) return;

    try {
      // Generate the current share URL
      const shareUrl = window.location.href;

      await navigator.clipboard.writeText(shareUrl);

      // Show success feedback
      const button = document.activeElement as HTMLButtonElement;
      if (button) {
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('btn-success');
        setTimeout(() => {
          button.textContent = originalText;
          button.classList.remove('btn-success');
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to copy link:', error);
      alert('Failed to copy download link');
    }
  };



  const isExpired = (expiresAt?: number) => {
    return expiresAt ? Date.now() > expiresAt : false;
  };

  const canDownload = () => {
    if (!transferInfo) return false;
    if (transferInfo.status !== 'ready') return false;
    if (isExpired(transferInfo.expiresAt)) return false;
    if (transferInfo.downloadLimit && transferInfo.downloadCount >= transferInfo.downloadLimit) return false;
    return true;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body items-center text-center">
            <span className="loading loading-spinner loading-lg text-primary"></span>
            <h2 className="card-title">Loading transfer...</h2>
            <p className="text-base-content/70">Please wait while we fetch your file</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="card bg-base-100 shadow-xl max-w-md">
          <div className="card-body items-center text-center">
            <AlertCircle className="w-16 h-16 text-error mb-4" />
            <h2 className="card-title text-error">Transfer Not Available</h2>
            <p className="text-base-content/70">{error}</p>
            <div className="card-actions justify-end mt-4">
              <button
                onClick={() => window.location.href = '/'}
                className="btn btn-primary"
              >
                Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!transferInfo) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="card bg-base-100 shadow-xl max-w-md">
          <div className="card-body items-center text-center">
            <AlertCircle className="w-16 h-16 text-warning mb-4" />
            <h2 className="card-title">Transfer Not Found</h2>
            <p className="text-base-content/70">The requested transfer could not be found.</p>
            <div className="card-actions justify-end mt-4">
              <button
                onClick={() => window.location.href = '/'}
                className="btn btn-primary"
              >
                Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4" data-theme="fasttransfer">
      <div className="max-w-2xl w-full">
        {/* Success Modal Card */}
        <div className="bg-black border border-gray-600 rounded-3xl p-8 text-center text-white">
          {/* Success Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-[#1FE183] rounded-lg flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-black" />
            </div>
          </div>

          {/* File Name and Success Message */}
          <h1 className="text-3xl font-bold mb-2">{transferInfo.originalName}</h1>
          <p className="text-xl text-gray-300 mb-6">has been uploaded successfully!</p>

          {/* Celebration Message */}
          <div className="bg-white text-black px-6 py-3 rounded-lg inline-block mb-8 transform rotate-1">
            <span className="font-bold text-lg">🤠 YEEEHAAAWWW! 🐎</span>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-3 gap-8 mb-8 border-t border-b border-gray-600 py-8">
            <div className="text-center">
              <div className="text-gray-400 text-sm uppercase tracking-wide mb-2">ORIGINAL SIZE</div>
              <div className="text-3xl font-bold">
                {formatFileSize(transferInfo.originalSize || transferInfo.size)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-400 text-sm uppercase tracking-wide mb-2">NEW SIZE</div>
              <div className="text-3xl font-bold">
                {transferInfo.compressedSize
                  ? formatFileSize(transferInfo.compressedSize)
                  : transferInfo.compressionRatio
                    ? formatFileSize(Math.round((transferInfo.originalSize || transferInfo.size) * transferInfo.compressionRatio))
                    : '7.2 MB'
                }
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-400 text-sm uppercase tracking-wide mb-2">COMPRESSION RATE</div>
              <div className="text-3xl font-bold">
                {transferInfo.compressedSize
                  ? `${Math.round(((transferInfo.originalSize || transferInfo.size) - transferInfo.compressedSize) / (transferInfo.originalSize || transferInfo.size) * 100)}%`
                  : transferInfo.compressionRatio
                    ? `${Math.round((1 - transferInfo.compressionRatio) * 100)}%`
                    : '98.2%'
                }
              </div>
            </div>
          </div>

          {/* Password Input (if required) */}
          {showPasswordInput && (
            <div className="mb-6">
              <div className="bg-yellow-900/30 border border-yellow-600 rounded-lg p-4 mb-4 flex items-center">
                <Lock className="w-5 h-5 text-yellow-400 mr-3" />
                <div>
                  <div className="font-bold text-yellow-400">Password Required</div>
                  <div className="text-sm text-gray-300">This transfer is password protected</div>
                </div>
              </div>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 pr-12"
                  placeholder="Enter the password to access this file"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5 text-gray-400" />
                  ) : (
                    <Eye className="w-5 h-5 text-gray-400" />
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4">
            {canDownload() ? (
              <>
                {downloading ? (
                  <div className="flex items-center justify-center p-4">
                    <span className="loading loading-spinner loading-lg text-[#1FE183]"></span>
                    <span className="ml-3 text-lg">Downloading...</span>
                  </div>
                ) : (
                  <>
                    {/* Cancel Button */}
                    <button
                      onClick={() => window.location.href = '/'}
                      className="px-6 py-3 bg-transparent border border-red-500 text-red-500 rounded-lg hover:bg-red-500 hover:text-white transition-colors"
                    >
                      Cancel
                    </button>

                    {/* Download Original Button */}
                    <button
                      onClick={() => handleDownload('original')}
                      disabled={showPasswordInput && !password}
                      className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:bg-gray-800 disabled:text-gray-500"
                    >
                      Download original
                    </button>

                    {/* Download Compressed Button */}
                    <button
                      onClick={() => handleDownload('compressed')}
                      disabled={showPasswordInput && !password}
                      className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:bg-gray-800 disabled:text-gray-500"
                    >
                      Download compressed
                    </button>

                    {/* Share Button */}
                    <button
                      onClick={handleCopyDownloadLink}
                      className="px-6 py-3 bg-[#1FE183] text-black font-bold rounded-lg hover:bg-[#1BC76B] transition-colors"
                    >
                      Share it!
                    </button>
                  </>
                )}
              </>
            ) : (
              <div className="bg-red-900/30 border border-red-600 rounded-lg p-4 flex items-center">
                <AlertCircle className="w-6 h-6 text-red-400 mr-3" />
                <div>
                  <div className="font-bold text-red-400">Download Not Available</div>
                  <div className="text-sm text-gray-300">
                    {isExpired(transferInfo.expiresAt)
                      ? 'This transfer has expired and is no longer available'
                      : transferInfo.downloadLimit && transferInfo.downloadCount >= transferInfo.downloadLimit
                      ? 'The download limit for this transfer has been reached'
                      : 'This transfer is not available for download'
                    }
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
