import { useState, useEffect } from 'react';
import { HardDrive, User, Crown, AlertTriangle, CheckCircle, ArrowUp } from 'lucide-react';
import { getUserStatus } from '../services/api';
import type { UserStatusResponse } from '../services/api';
import { formatFileSize } from '../utils/formatters';

interface UserStatusProps {
  email: string;
  onUpgradeClick?: () => void;
}

export default function UserStatus({ email, onUpgradeClick }: UserStatusProps) {
  const [userStatus, setUserStatus] = useState<UserStatusResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchUserStatus = async () => {
      if (!email) return;
      
      try {
        setLoading(true);
        const status = await getUserStatus(email);
        setUserStatus(status);
      } catch (error: any) {
        console.error('Failed to fetch user status:', error);
        setError('Failed to load user status');
      } finally {
        setLoading(false);
      }
    };

    fetchUserStatus();
  }, [email]);



  const getUsagePercentage = () => {
    if (!userStatus) return 0;
    return Math.min((userStatus.dataUploaded / userStatus.dataLimit) * 100, 100);
  };

  const getStatusColor = () => {
    if (!userStatus) return 'text-gray-500';
    return userStatus.status === 'active' ? 'text-green-600' : 'text-orange-600';
  };

  const getStatusIcon = () => {
    if (!userStatus) return <User className="w-4 h-4" />;
    return userStatus.status === 'active' ? 
      <Crown className="w-4 h-4 text-yellow-500" /> : 
      <User className="w-4 h-4" />;
  };

  const getProgressBarColor = () => {
    const percentage = getUsagePercentage();
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    return 'bg-primary';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-2 bg-gray-200 rounded w-full mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (error || !userStatus) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-4">
        <div className="flex items-center text-red-600">
          <AlertTriangle className="w-4 h-4 mr-2" />
          <span className="text-sm">{error || 'Unable to load user status'}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`font-medium text-sm ${getStatusColor()}`}>
            {userStatus.status === 'active' ? 'Premium Account' : 'Starter Account'}
          </span>
        </div>
        
        {userStatus.status === 'pending' && (
          <button
            onClick={onUpgradeClick}
            className="btn btn-xs bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 hover:from-purple-700 hover:to-blue-700"
          >
            <ArrowUp className="w-3 h-3 mr-1" />
            Upgrade
          </button>
        )}
      </div>

      {/* Storage Usage */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <HardDrive className="w-4 h-4 text-gray-500" />
            <span className="text-gray-700">Storage Used</span>
          </div>
          <span className="font-medium text-gray-900">
            {formatFileSize(userStatus.dataUploaded)} / {formatFileSize(userStatus.dataLimit)}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
            style={{ width: `${getUsagePercentage()}%` }}
          ></div>
        </div>

        <div className="text-xs text-gray-500">
          {userStatus.canUpload ? (
            <span className="flex items-center">
              <CheckCircle className="w-3 h-3 text-green-500 mr-1" />
              {formatFileSize(userStatus.dataLimit - userStatus.dataUploaded)} remaining
            </span>
          ) : (
            <span className="flex items-center text-red-600">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Storage limit reached
            </span>
          )}
        </div>
      </div>

      {/* Upgrade Prompt */}
      {userStatus.upgradeRequired && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-3 border border-purple-200">
          <div className="flex items-start space-x-2">
            <Crown className="w-4 h-4 text-purple-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm font-medium text-purple-900 mb-1">
                Upgrade to Premium
              </p>
              <p className="text-xs text-purple-700 mb-2">
                Get 1TB of free storage and unlock all premium features!
              </p>
              <button
                onClick={onUpgradeClick}
                className="btn btn-xs bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 hover:from-purple-700 hover:to-blue-700"
              >
                Upgrade Now - Free!
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Account Benefits */}
      <div className="text-xs text-gray-500">
        {userStatus.status === 'active' ? (
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <CheckCircle className="w-3 h-3 text-green-500 mr-1" />
              1TB Storage
            </span>
            <span className="flex items-center">
              <CheckCircle className="w-3 h-3 text-green-500 mr-1" />
              Premium Features
            </span>
          </div>
        ) : (
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <CheckCircle className="w-3 h-3 text-primary mr-1" />
              100MB Storage
            </span>
            <span className="text-gray-400">
              Upgrade for 1TB free
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
