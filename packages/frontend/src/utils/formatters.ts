/**
 * Utility functions for formatting various data types
 */

/**
 * Format file sizes with appropriate units (B, KB, MB, GB, TB)
 * @param bytes - Size in bytes
 * @returns Formatted string with appropriate unit
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  // Ensure we don't exceed the units array
  const unitIndex = Math.min(i, units.length - 1);
  const size = bytes / Math.pow(k, unitIndex);

  // Format with appropriate decimal places
  const formattedSize = unitIndex === 0 ? size.toString() : size.toFixed(1);

  return `${formattedSize} ${units[unitIndex]}`;
};

/**
 * Format speed with appropriate units per second
 * @param bytesPerSecond - Speed in bytes per second
 * @returns Formatted string with appropriate unit per second
 */
export const formatSpeed = (bytesPerSecond: number): string => {
  return formatFileSize(bytesPerSecond) + '/s';
};

/**
 * Format time duration in a human-readable format
 * @param milliseconds - Duration in milliseconds
 * @returns Formatted string (e.g., "2h 30m", "45s")
 */
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * Format date in a consistent format
 * @param dateString - Date string or timestamp
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | number): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
