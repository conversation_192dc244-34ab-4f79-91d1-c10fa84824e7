AWSTemplateFormatVersion: '2010-09-09'
Description: 'FastTransfer EC2 Upload Infrastructure - Direct-to-EC2 Architecture'

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name
  
  InstanceType:
    Type: String
    Default: c5.2xlarge
    AllowedValues: [c5.large, c5.xlarge, c5.2xlarge, c5.4xlarge, c5.9xlarge]
    Description: EC2 instance type for upload processing
  
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access
  
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where resources will be created
  
  SubnetIds:
    Type: List<AWS::EC2::Subnet::Id>
    Description: Subnet IDs for EC2 instances (minimum 2 for ALB)
  
  SSLCertificateArn:
    Type: String
    Description: ARN of SSL certificate for HTTPS
  
  DomainName:
    Type: String
    Description: Domain name for the upload service
    Default: upload.fasttransfer.com

Resources:
  # IAM Role for EC2 instances
  EC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'FastTransfer-EC2-Role-${Environment}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !Sub '${CompressedBucket}/*'
                  - !Sub '${CompressedBucket}'
              - Effect: Allow
                Action:
                  - s3:GetBucketLocation
                Resource: '*'
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                Resource: '*'

  # Instance Profile
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      InstanceProfileName: !Sub 'FastTransfer-EC2-Profile-${Environment}'
      Roles:
        - !Ref EC2Role

  # Security Group for EC2 instances
  EC2SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub 'FastTransfer-EC2-SG-${Environment}'
      GroupDescription: Security group for FastTransfer EC2 upload instances
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          SourceSecurityGroupId: !Ref ALBSecurityGroup
          Description: HTTP from ALB
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 10.0.0.0/8
          Description: SSH from VPC
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic

  # Security Group for Application Load Balancer
  ALBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub 'FastTransfer-ALB-SG-${Environment}'
      GroupDescription: Security group for FastTransfer Application Load Balancer
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: HTTPS from internet
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
          Description: HTTP from internet (redirect to HTTPS)
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic

  # Launch Template for EC2 instances
  LaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Properties:
      LaunchTemplateName: !Sub 'FastTransfer-LaunchTemplate-${Environment}'
      LaunchTemplateData:
        ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2023 AMI (update as needed)
        InstanceType: !Ref InstanceType
        KeyName: !Ref KeyPairName
        IamInstanceProfile:
          Arn: !GetAtt EC2InstanceProfile.Arn
        SecurityGroupIds:
          - !Ref EC2SecurityGroup
        BlockDeviceMappings:
          - DeviceName: /dev/xvda
            Ebs:
              VolumeSize: 100  # 100GB root volume
              VolumeType: gp3
              Iops: 3000
              Throughput: 125
              DeleteOnTermination: true
          - DeviceName: /dev/xvdf
            Ebs:
              VolumeSize: 1000  # 1TB data volume for uploads/processing
              VolumeType: gp3
              Iops: 16000
              Throughput: 1000
              DeleteOnTermination: true
        UserData:
          Fn::Base64: !Sub |
            #!/bin/bash
            yum update -y
            
            # Install Node.js 18
            curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
            yum install -y nodejs
            
            # Install development tools
            yum groupinstall -y "Development Tools"
            yum install -y git htop iotop
            
            # Format and mount data volume
            mkfs -t ext4 /dev/xvdf
            mkdir -p /mnt/data
            mount /dev/xvdf /mnt/data
            echo '/dev/xvdf /mnt/data ext4 defaults,nofail 0 2' >> /etc/fstab
            
            # Create directories
            mkdir -p /mnt/data/uploads
            mkdir -p /mnt/data/work
            mkdir -p /var/log/fasttransfer
            
            # Set permissions
            chown -R ec2-user:ec2-user /mnt/data
            chown -R ec2-user:ec2-user /var/log/fasttransfer
            
            # Install CloudWatch agent
            yum install -y amazon-cloudwatch-agent
            
            # Create CloudWatch agent config
            cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
            {
              "metrics": {
                "namespace": "FastTransfer/EC2",
                "metrics_collected": {
                  "cpu": {
                    "measurement": ["cpu_usage_idle", "cpu_usage_iowait", "cpu_usage_user", "cpu_usage_system"],
                    "metrics_collection_interval": 60
                  },
                  "disk": {
                    "measurement": ["used_percent"],
                    "metrics_collection_interval": 60,
                    "resources": ["*"]
                  },
                  "diskio": {
                    "measurement": ["io_time", "read_bytes", "write_bytes", "reads", "writes"],
                    "metrics_collection_interval": 60,
                    "resources": ["*"]
                  },
                  "mem": {
                    "measurement": ["mem_used_percent"],
                    "metrics_collection_interval": 60
                  },
                  "netstat": {
                    "measurement": ["tcp_established", "tcp_time_wait"],
                    "metrics_collection_interval": 60
                  }
                }
              },
              "logs": {
                "logs_collected": {
                  "files": {
                    "collect_list": [
                      {
                        "file_path": "/var/log/fasttransfer/*.log",
                        "log_group_name": "/aws/ec2/fasttransfer/${Environment}",
                        "log_stream_name": "{instance_id}/application"
                      }
                    ]
                  }
                }
              }
            }
            EOF
            
            # Start CloudWatch agent
            /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
              -a fetch-config -m ec2 -s \
              -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
            
            # Set environment variables
            echo "export EC2_UPLOAD_DIR=/mnt/data/uploads" >> /home/<USER>/.bashrc
            echo "export EC2_WORK_DIR=/mnt/data/work" >> /home/<USER>/.bashrc
            echo "export AWS_REGION=${AWS::Region}" >> /home/<USER>/.bashrc
            echo "export COMPRESSED_BUCKET=${CompressedBucket}" >> /home/<USER>/.bashrc
            echo "export NODE_ENV=${Environment}" >> /home/<USER>/.bashrc
            
            # Signal completion
            /opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource AutoScalingGroup --region ${AWS::Region}

  # S3 Bucket for compressed files
  CompressedBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'fasttransfer-compressed-${Environment}-${AWS::AccountId}'
      VersioningConfiguration:
        Status: Enabled
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 7
          - Id: DeleteExpiredFiles
            Status: Enabled
            ExpirationInDays: 30
      NotificationConfiguration:
        CloudWatchConfigurations:
          - Event: s3:ObjectCreated:*
            CloudWatchConfiguration:
              LogGroupName: !Ref S3LogGroup

  # CloudWatch Log Group for S3 events
  S3LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/s3/fasttransfer-compressed-${Environment}'
      RetentionInDays: 30

  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub 'FastTransfer-ALB-${Environment}'
      Type: application
      Scheme: internet-facing
      SecurityGroups:
        - !Ref ALBSecurityGroup
      Subnets: !Ref SubnetIds
      Tags:
        - Key: Name
          Value: !Sub 'FastTransfer-ALB-${Environment}'

  # Target Group
  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub 'FastTransfer-TG-${Environment}'
      Port: 3000
      Protocol: HTTP
      VpcId: !Ref VpcId
      HealthCheckPath: /api/health
      HealthCheckProtocol: HTTP
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 10
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 5
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '60'
        - Key: stickiness.enabled
          Value: 'true'
        - Key: stickiness.type
          Value: lb_cookie
        - Key: stickiness.lb_cookie.duration_seconds
          Value: '86400'  # 24 hours

  # HTTPS Listener
  HTTPSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref TargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref SSLCertificateArn

  # HTTP Listener (redirect to HTTPS)
  HTTPListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: 443
            StatusCode: HTTP_301
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP

  # Auto Scaling Group
  AutoScalingGroup:
    Type: AWS::AutoScaling::AutoScalingGroup
    Properties:
      AutoScalingGroupName: !Sub 'FastTransfer-ASG-${Environment}'
      LaunchTemplate:
        LaunchTemplateId: !Ref LaunchTemplate
        Version: !GetAtt LaunchTemplate.LatestVersionNumber
      MinSize: 1
      MaxSize: 10
      DesiredCapacity: 2
      VPCZoneIdentifier: !Ref SubnetIds
      TargetGroupARNs:
        - !Ref TargetGroup
      HealthCheckType: ELB
      HealthCheckGracePeriod: 300
      Tags:
        - Key: Name
          Value: !Sub 'FastTransfer-Instance-${Environment}'
          PropagateAtLaunch: true
    CreationPolicy:
      ResourceSignal:
        Count: 1
        Timeout: PT15M
    UpdatePolicy:
      AutoScalingRollingUpdate:
        MinInstancesInService: 1
        MaxBatchSize: 1
        PauseTime: PT15M
        WaitOnResourceSignals: true

Outputs:
  LoadBalancerDNS:
    Description: DNS name of the load balancer
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub '${AWS::StackName}-LoadBalancerDNS'
  
  CompressedBucketName:
    Description: Name of the S3 bucket for compressed files
    Value: !Ref CompressedBucket
    Export:
      Name: !Sub '${AWS::StackName}-CompressedBucket'
  
  AutoScalingGroupName:
    Description: Name of the Auto Scaling Group
    Value: !Ref AutoScalingGroup
    Export:
      Name: !Sub '${AWS::StackName}-AutoScalingGroup'
