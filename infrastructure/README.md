# FastTransfer Infrastructure

This directory contains the infrastructure code for deploying FastTransfer using both AWS CDK and CloudFormation.

## Architecture Overview

FastTransfer supports two deployment architectures:

### 1. Original Architecture (CDK)
- Files uploaded to S3 first
- SQS queue triggers worker processing
- Worker downloads, compresses, and re-uploads to S3

### 2. New EC2 Direct Upload Architecture (CloudFormation)
- Files uploaded directly to EC2 instances
- Compression happens locally on EC2
- Only compressed files uploaded to S3
- Eliminates download-compress-upload cycle

## Quick Start

### Deploy EC2 Direct Upload Architecture (Recommended)

```bash
# Deploy development environment
./deploy-ec2.sh -e dev \
  -k your-key-pair \
  -v vpc-12345678 \
  -n subnet-12345,subnet-67890 \
  -c arn:aws:acm:us-east-1:123456789012:certificate/your-cert-id

# Deploy production environment
./deploy-ec2.sh -e prod \
  -t c5.4xlarge \
  -k prod-key-pair \
  -v vpc-prod123 \
  -n subnet-prod1,subnet-prod2 \
  -c arn:aws:acm:us-east-1:123456789012:certificate/prod-cert-id
```

### Deploy Original Architecture (CDK)

```bash
# Install dependencies
npm install

# Deploy using CDK
./deploy.sh dev us-east-1 your-key-pair
```

## EC2 Direct Upload Architecture

### Components

- **Application Load Balancer**: Routes HTTPS traffic to EC2 instances
- **Auto Scaling Group**: Manages EC2 instances (1-10 instances)
- **EC2 Instances**: Process uploads and compression locally
- **S3 Bucket**: Stores compressed files only
- **CloudWatch**: Monitoring and logging
- **IAM Roles**: Secure access to AWS services

### Instance Configuration

- **Instance Type**: c5.2xlarge (default, configurable)
- **Storage**: 
  - 100GB root volume (gp3)
  - 1TB data volume (gp3, high IOPS)
- **Software**: Node.js 18, development tools, CloudWatch agent
- **Directories**:
  - `/mnt/data/uploads`: Temporary upload storage
  - `/mnt/data/work`: Compression working directory
  - `/var/log/fasttransfer`: Application logs

### Security Features

- **Rate Limiting**: 5 uploads per hour per IP, 100 API requests per 15 minutes
- **File Validation**: Size limits (100GB), type filtering, filename validation
- **Security Headers**: Helmet.js security middleware
- **HTTPS Only**: SSL/TLS encryption required
- **VPC Security Groups**: Restricted network access
- **IAM Roles**: Least privilege access

### Monitoring & Analytics

- **Upload Metrics**: Compression ratios, processing times, file sizes
- **System Metrics**: CPU, memory, disk usage, network I/O
- **CloudWatch Integration**: Automated log collection and metrics
- **Health Checks**: Application and infrastructure health monitoring

## Prerequisites

### For EC2 Architecture

1. **AWS CLI**: Installed and configured
2. **VPC Setup**: Existing VPC with public subnets
3. **SSL Certificate**: ACM certificate for HTTPS
4. **Key Pair**: EC2 key pair for SSH access
5. **Domain**: Optional custom domain name

### For CDK Architecture

1. **Node.js**: Version 16 or higher
2. **AWS CDK**: Installed globally (`npm install -g aws-cdk`)
3. **AWS CLI**: Installed and configured

## Configuration

### Environment Variables

```bash
# EC2 Architecture
export EC2_UPLOAD_DIR=/mnt/data/uploads
export EC2_WORK_DIR=/mnt/data/work
export AWS_REGION=us-east-1
export COMPRESSED_BUCKET=your-compressed-bucket
export NODE_ENV=production

# Application Configuration
export MAX_FILE_SIZE=107374182400  # 100GB
export UPLOAD_RATE_LIMIT=5         # uploads per hour
export API_RATE_LIMIT=100          # requests per 15 minutes
```

### Instance Types

| Type | vCPUs | Memory | Network | Use Case |
|------|-------|--------|---------|----------|
| c5.large | 2 | 4 GB | Up to 10 Gbps | Development |
| c5.xlarge | 4 | 8 GB | Up to 10 Gbps | Small production |
| c5.2xlarge | 8 | 16 GB | Up to 10 Gbps | Medium production |
| c5.4xlarge | 16 | 32 GB | Up to 10 Gbps | Large production |
| c5.9xlarge | 36 | 72 GB | 10 Gbps | High volume |

## Deployment Options

### Development Environment

```bash
./deploy-ec2.sh -e dev -k dev-key -v vpc-dev -n subnet-dev1,subnet-dev2 -c cert-arn
```

### Staging Environment

```bash
./deploy-ec2.sh -e staging -t c5.xlarge -k staging-key -v vpc-staging -n subnet-staging1,subnet-staging2 -c cert-arn
```

### Production Environment

```bash
./deploy-ec2.sh -e prod -t c5.4xlarge -k prod-key -v vpc-prod -n subnet-prod1,subnet-prod2 -c cert-arn
```

## Monitoring

### CloudWatch Metrics

- **Application Metrics**: Upload success rate, compression ratios, processing times
- **System Metrics**: CPU utilization, memory usage, disk I/O, network throughput
- **Custom Metrics**: File sizes, compression efficiency, error rates

### Log Groups

- `/aws/ec2/fasttransfer/{environment}`: Application logs
- `/aws/s3/fasttransfer-compressed-{environment}`: S3 access logs

### Alarms

- High CPU utilization (>80%)
- High memory usage (>90%)
- Disk space usage (>85%)
- Upload failure rate (>5%)

## Scaling

### Auto Scaling Policies

- **Scale Out**: CPU > 70% for 5 minutes
- **Scale In**: CPU < 30% for 10 minutes
- **Min Instances**: 1
- **Max Instances**: 10

### Load Balancer

- **Health Checks**: `/api/health` endpoint
- **Sticky Sessions**: Enabled for upload continuity
- **SSL Termination**: At load balancer level

## Troubleshooting

### Common Issues

1. **Stack Creation Failed**
   - Check VPC and subnet IDs
   - Verify SSL certificate ARN
   - Ensure key pair exists in the region

2. **Instances Not Healthy**
   - Check security group rules
   - Verify user data script execution
   - Review CloudWatch logs

3. **Upload Failures**
   - Check disk space on instances
   - Verify S3 bucket permissions
   - Review rate limiting settings

### Useful Commands

```bash
# Check stack status
aws cloudformation describe-stacks --stack-name fasttransfer-ec2-dev

# View instance logs
aws logs tail /aws/ec2/fasttransfer/dev --follow

# SSH to instance
ssh -i your-key.pem ec2-user@instance-ip

# Check application status
curl https://your-domain/api/health
```

## Cost Optimization

### Recommendations

1. **Instance Scheduling**: Stop instances during off-hours for dev/staging
2. **Spot Instances**: Use for non-critical workloads
3. **Storage Optimization**: Use lifecycle policies for S3
4. **Reserved Instances**: For predictable production workloads

### Estimated Costs (us-east-1)

| Component | Dev | Staging | Production |
|-----------|-----|---------|------------|
| EC2 (c5.2xlarge) | $70/month | $140/month | $280/month |
| EBS Storage | $20/month | $40/month | $80/month |
| Load Balancer | $20/month | $20/month | $20/month |
| Data Transfer | $10/month | $50/month | $200/month |
| **Total** | **~$120/month** | **~$250/month** | **~$580/month** |

## Security Best Practices

1. **Network Security**: Use private subnets for instances when possible
2. **Access Control**: Implement least privilege IAM policies
3. **Encryption**: Enable EBS encryption and S3 encryption
4. **Monitoring**: Set up CloudTrail for API logging
5. **Updates**: Regularly update AMIs and security patches

## Support

For issues and questions:
1. Check CloudWatch logs first
2. Review CloudFormation events
3. Verify security group and IAM permissions
4. Test with smaller files first
