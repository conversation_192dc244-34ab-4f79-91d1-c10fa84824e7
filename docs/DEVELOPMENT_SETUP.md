# FastTransfer Development Setup Guide

This guide explains how to set up and run the FastTransfer platform for local development.

## Quick Start

### One-Command Launch (Recommended)

Launch all services with a single command:

```bash
# Using Node.js script (recommended)
npm run dev:all

# Or using shell script
npm run dev:shell
# or directly: ./scripts/dev-start.sh
```

This will start:
- **Frontend** (Vite dev server) on `http://localhost:5173`
- **Backend API** (Express server) on `http://localhost:3000`
- **Worker Service** (ZMT compression) - Currently disabled due to TypeScript compilation errors

### Manual Launch

If you prefer to start services individually:

```bash
# Terminal 1 - Frontend
cd packages/frontend
npm run dev

# Terminal 2 - Backend
cd services/backend
npm run dev:watch

# Terminal 3 - Worker Service
cd services/worker
npm run dev
```

## Prerequisites

### Required Software

- **Node.js** (v18 or higher)
- **npm** (v9 or higher)
- **Git**

### System Dependencies

For ZMT compression functionality:
- **Python 3.8+** (for ZMT compression scripts)
- **Build tools** (gcc, make) for native dependencies

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd fast-transfer
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Install ZMT compression tools:**
   ```bash
   cd scripts
   chmod +x zmt zmtdcm
   # Ensure Python dependencies are available
   pip install -r requirements.txt  # if requirements.txt exists
   ```

## Development Scripts

### Main Development Commands

| Command | Description | Ports |
|---------|-------------|-------|
| `npm run dev:all` | Start all services (Node.js script) | 3000, 3001, 5173 |
| `npm run dev:shell` | Start all services (Shell script) | 3000, 3001, 5173 |
| `npm run dev` | Start frontend + backend only | 3000, 5173 |

### Individual Service Commands

| Service | Command | Port | Description |
|---------|---------|------|-------------|
| Frontend | `npm run dev --workspace=frontend` | 5173 | Vite dev server with HMR |
| Backend | `npm run dev:watch --workspace=backend` | 3000 | Express API with auto-reload |
| Worker | `npm run dev --workspace=worker` | 3001 | ZMT compression service |

### Testing Commands

| Command | Description |
|---------|-------------|
| `npm test` | Run all tests |
| `npm run test:cache` | Test cache behaviors (local) |
| `npm run test:cache:prod` | Test cache behaviors (production) |
| `npm run performance` | Run performance tests |

### Build & Deploy Commands

| Command | Description |
|---------|-------------|
| `npm run build` | Build all services |
| `npm run lint` | Lint all code |
| `npm run format` | Format all code |
| `npm run deploy:infra` | Deploy infrastructure |
| `npm run deploy:backend` | Deploy backend service |
| `npm run deploy:frontend` | Deploy frontend |

## Service Details

### Frontend (Port 5173)

- **Framework**: React + Vite
- **UI Library**: DaisyUI + Tailwind CSS
- **Features**: File upload, download, sharing, compression
- **Hot Reload**: Enabled
- **Build Output**: `packages/frontend/dist/`

**Key Files:**
- `packages/frontend/src/App.tsx` - Main application
- `packages/frontend/src/components/` - React components
- `packages/frontend/vite.config.ts` - Vite configuration

### Backend API (Port 3000)

- **Framework**: Express.js + TypeScript
- **Features**: File upload/download, sharing, analytics
- **Auto-reload**: Enabled with ts-node --watch
- **Upload Directory**: `services/backend/uploads/`

**Key Files:**
- `services/backend/src/server.ts` - Main server
- `services/backend/src/lib/` - Service libraries
- `services/backend/src/routes/` - API routes

**API Endpoints:**
- `POST /api/upload` - File upload
- `GET /api/download/:transferId` - File download
- `GET /api/share/:shareId` - Share link access
- `GET /api/analytics/:transferId` - Transfer analytics

### Worker Service (Port 3001)

- **Purpose**: ZMT compression/decompression
- **Framework**: Express.js + TypeScript
- **Features**: File compression, decompression, health checks
- **Dependencies**: Python scripts for ZMT processing

**Key Files:**
- `services/worker/src/worker.ts` - Main worker service
- `scripts/zmt` - ZMT compression binary
- `scripts/zmtdcm` - ZMT decompression binary

**API Endpoints:**
- `POST /compress` - Compress files
- `POST /decompress` - Decompress files
- `GET /health` - Health check

## Development Workflow

### 1. Start Development Environment

```bash
npm run dev:all
```

Wait for all services to show "ready" status:
```
✓ Frontend ready! (http://localhost:5173)
✓ Backend ready! (http://localhost:3000)
✓ Worker ready! (http://localhost:3001)
```

### 2. Make Changes

- **Frontend changes**: Auto-reload via Vite HMR
- **Backend changes**: Auto-reload via ts-node --watch
- **Worker changes**: Auto-reload via ts-node --watch

### 3. Test Changes

```bash
# Test file upload/download
curl -X POST http://localhost:3000/api/upload -F "file=@test.txt"

# Test compression
curl -X POST http://localhost:3001/compress -F "file=@test.txt"

# Test cache behaviors
npm run test:cache
```

### 4. Stop Services

Press `Ctrl+C` in the terminal running the dev script. All services will be gracefully shut down.

## Troubleshooting

### Worker Service Disabled

The worker service is currently disabled due to TypeScript compilation errors. To enable it:

1. **Fix missing dependencies**:
   ```bash
   cd services/worker
   npm install fs-extra @types/fs-extra
   ```

2. **Fix TypeScript errors in `services/worker/src/worker.ts`**:
   - Change `VisibilityTimeoutSeconds` to `VisibilityTimeout` in AWS SQS configuration
   - Add proper error type handling for `catch` blocks
   - Fix const assignment issues with `updateExpression` variables

3. **Uncomment worker service configuration** in `scripts/dev-start.js`

### Port Conflicts

If ports are already in use:

```bash
# Check what's using the ports
lsof -i :3000
lsof -i :3001
lsof -i :5173

# Kill processes if needed
kill -9 <PID>
```

### Missing Dependencies

```bash
# Reinstall all dependencies
rm -rf node_modules packages/*/node_modules services/*/node_modules
npm install
```

### ZMT Compression Issues

```bash
# Check ZMT scripts are executable
ls -la scripts/zmt scripts/zmtdcm

# Make executable if needed
chmod +x scripts/zmt scripts/zmtdcm

# Test ZMT compression
echo "test" | ./scripts/zmt > test.zmt
./scripts/zmtdcm test.zmt
```

### Service Not Starting

1. Check the service logs in the terminal
2. Verify package.json scripts exist
3. Ensure all dependencies are installed
4. Check for TypeScript compilation errors

### Common Issues

| Issue | Solution |
|-------|----------|
| "Port already in use" | Kill existing processes or change ports |
| "Module not found" | Run `npm install` in the specific service directory |
| "Permission denied" | Make scripts executable with `chmod +x` |
| "TypeScript errors" | Fix compilation errors in the affected service |
| "ZMT not working" | Ensure Python and ZMT scripts are properly installed |

## Environment Variables

### Backend (.env)

```bash
NODE_ENV=development
PORT=3000
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100GB
SENDGRID_API_KEY=your_sendgrid_key
```

### Worker (.env)

```bash
NODE_ENV=development
PORT=3001
ZMT_SCRIPT_PATH=../../scripts/zmt
ZMTDCM_SCRIPT_PATH=../../scripts/zmtdcm
```

### Frontend (.env)

```bash
VITE_API_URL=http://localhost:3000
VITE_WORKER_URL=http://localhost:3001
```

## Performance Tips

1. **Use SSD storage** for faster file I/O
2. **Increase Node.js memory** for large files: `NODE_OPTIONS="--max-old-space-size=4096"`
3. **Enable file watching exclusions** in your IDE for `node_modules` and `dist` folders
4. **Use npm workspaces** for efficient dependency management

## Next Steps

- Review the [Cache Configuration Guide](./CACHE_CONFIGURATION.md)
- Check the [Testing Guide](../FRONTEND_TESTING_GUIDE.md)
- Explore the [Performance Testing](../scripts/performance-test.js)

For production deployment, see the deployment scripts in the `scripts/` directory.
