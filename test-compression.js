const path = require('path');
const fs = require('fs');

// Import the compression router
const { CompressionRouter } = require('./services/worker/dist/compression-router');

async function testCompression() {
  console.log('🧪 Testing ZMT Compression System');
  
  const scriptsDir = path.join(__dirname, 'scripts');
  const compressionRouter = new CompressionRouter(scriptsDir);
  
  // Test files
  const testFiles = [
    { name: 'test.txt', type: 'basic' },
    { name: 'test.pdf', type: 'basic' },
    { name: 'test.csv', type: 'basic' }
  ];
  
  for (const testFile of testFiles) {
    try {
      console.log(`\n📁 Testing ${testFile.name}...`);
      
      const inputPath = path.join(__dirname, 'test-files', testFile.name);
      const outputPath = path.join(__dirname, 'test-files', `${testFile.name}.zmt`);
      
      // Check if input file exists
      if (!fs.existsSync(inputPath)) {
        console.log(`❌ Input file ${inputPath} does not exist`);
        continue;
      }
      
      // Get compression method
      const method = compressionRouter.getCompressionMethod(testFile.name);
      console.log(`🔍 Detected compression method: ${method}`);
      
      // Test compression
      const startTime = Date.now();
      const result = await compressionRouter.compressFiles([inputPath], outputPath, method);
      const endTime = Date.now();
      
      console.log(`✅ Compression completed in ${endTime - startTime}ms`);
      console.log(`📊 Results:`, {
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        compressionTime: result.compressionTime,
        method: result.method
      });
      
      // Check if output file was created
      if (fs.existsSync(outputPath)) {
        console.log(`✅ Compressed file created: ${outputPath}`);
      } else {
        console.log(`❌ Compressed file not found: ${outputPath}`);
      }
      
    } catch (error) {
      console.error(`❌ Error testing ${testFile.name}:`, error.message);
    }
  }
}

// Run the test
testCompression().catch(console.error);
