import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';

export interface ChunkMetadata {
  transferId: string;
  originalFilename: string;
  totalSize: number;
  totalChunks: number;
  chunkSize: number;
  uploadedChunks: Set<number>;
  createdAt: number;
  lastActivity: number;
}

export interface ChunkUploadResult {
  success: boolean;
  chunkIndex: number;
  isComplete: boolean;
  uploadedChunks: number;
  totalChunks: number;
  error?: string;
}

export class ChunkManager {
  private readonly chunksDir: string;
  private readonly completeDir: string;
  private readonly metadataDir: string;
  private readonly chunkMetadata: Map<string, ChunkMetadata> = new Map();
  
  // 25MB chunk size for optimal compression efficiency
  public static readonly CHUNK_SIZE = 25 * 1024 * 1024;
  
  // Cleanup chunks older than 24 hours
  private static readonly CLEANUP_TIMEOUT = 24 * 60 * 60 * 1000;

  constructor(baseDir: string = './uploads') {
    this.chunksDir = path.join(baseDir, 'chunks');
    this.completeDir = path.join(baseDir, 'complete');
    this.metadataDir = path.join(baseDir, 'metadata');
    
    this.ensureDirectories();
    this.loadExistingMetadata();
    this.startCleanupTimer();
  }

  private async ensureDirectories(): Promise<void> {
    await fs.ensureDir(this.chunksDir);
    await fs.ensureDir(this.completeDir);
    await fs.ensureDir(this.metadataDir);
  }

  private async loadExistingMetadata(): Promise<void> {
    try {
      const metadataFiles = await fs.readdir(this.metadataDir);
      
      for (const file of metadataFiles) {
        if (file.endsWith('.json')) {
          const transferId = file.replace('.json', '');
          const metadataPath = path.join(this.metadataDir, file);
          const metadata = await fs.readJson(metadataPath);
          
          // Convert uploaded chunks array back to Set
          metadata.uploadedChunks = new Set(metadata.uploadedChunks || []);
          this.chunkMetadata.set(transferId, metadata);
        }
      }
      
      console.log(`📋 Loaded metadata for ${this.chunkMetadata.size} transfers`);
    } catch (error) {
      console.error('Failed to load existing metadata:', error);
    }
  }

  private async saveMetadata(transferId: string): Promise<void> {
    const metadata = this.chunkMetadata.get(transferId);
    if (!metadata) return;

    const metadataPath = path.join(this.metadataDir, `${transferId}.json`);
    
    // Convert Set to array for JSON serialization
    const serializable = {
      ...metadata,
      uploadedChunks: Array.from(metadata.uploadedChunks)
    };
    
    await fs.writeJson(metadataPath, serializable);
  }

  public async initializeUpload(
    transferId: string,
    originalFilename: string,
    totalSize: number
  ): Promise<ChunkMetadata> {
    const totalChunks = Math.ceil(totalSize / ChunkManager.CHUNK_SIZE);
    
    const metadata: ChunkMetadata = {
      transferId,
      originalFilename,
      totalSize,
      totalChunks,
      chunkSize: ChunkManager.CHUNK_SIZE,
      uploadedChunks: new Set(),
      createdAt: Date.now(),
      lastActivity: Date.now()
    };

    this.chunkMetadata.set(transferId, metadata);
    
    // Create transfer directory
    const transferDir = path.join(this.chunksDir, transferId);
    await fs.ensureDir(transferDir);
    
    // Save metadata
    await this.saveMetadata(transferId);
    
    console.log(`📦 Initialized chunked upload: ${originalFilename} (${totalChunks} chunks of ${(ChunkManager.CHUNK_SIZE / 1024 / 1024).toFixed(0)}MB)`);
    
    return metadata;
  }

  public async storeChunk(
    transferId: string,
    chunkIndex: number,
    chunkData: Buffer
  ): Promise<ChunkUploadResult> {
    const metadata = this.chunkMetadata.get(transferId);
    
    if (!metadata) {
      return {
        success: false,
        chunkIndex,
        isComplete: false,
        uploadedChunks: 0,
        totalChunks: 0,
        error: 'Transfer not initialized'
      };
    }

    // Validate chunk index
    if (chunkIndex < 0 || chunkIndex >= metadata.totalChunks) {
      return {
        success: false,
        chunkIndex,
        isComplete: false,
        uploadedChunks: metadata.uploadedChunks.size,
        totalChunks: metadata.totalChunks,
        error: `Invalid chunk index: ${chunkIndex}`
      };
    }

    // Store chunk to disk
    const chunkPath = path.join(this.chunksDir, transferId, `chunk-${chunkIndex}.part`);
    
    try {
      await fs.writeFile(chunkPath, chunkData);
      
      // Update metadata
      metadata.uploadedChunks.add(chunkIndex);
      metadata.lastActivity = Date.now();
      
      // Save updated metadata
      await this.saveMetadata(transferId);
      
      const isComplete = metadata.uploadedChunks.size === metadata.totalChunks;
      
      console.log(`📤 Stored chunk ${chunkIndex + 1}/${metadata.totalChunks} for ${transferId} (${(chunkData.length / 1024 / 1024).toFixed(2)}MB)`);
      
      return {
        success: true,
        chunkIndex,
        isComplete,
        uploadedChunks: metadata.uploadedChunks.size,
        totalChunks: metadata.totalChunks
      };
      
    } catch (error) {
      console.error(`Failed to store chunk ${chunkIndex} for ${transferId}:`, error);
      
      return {
        success: false,
        chunkIndex,
        isComplete: false,
        uploadedChunks: metadata.uploadedChunks.size,
        totalChunks: metadata.totalChunks,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  public async reassembleFile(transferId: string): Promise<string> {
    const metadata = this.chunkMetadata.get(transferId);
    
    if (!metadata) {
      throw new Error(`Transfer metadata not found: ${transferId}`);
    }

    if (metadata.uploadedChunks.size !== metadata.totalChunks) {
      throw new Error(`Incomplete upload: ${metadata.uploadedChunks.size}/${metadata.totalChunks} chunks`);
    }

    const outputPath = path.join(this.completeDir, `${transferId}-${metadata.originalFilename}`);
    const writeStream = fs.createWriteStream(outputPath);
    
    console.log(`🔗 Reassembling ${metadata.totalChunks} chunks for ${metadata.originalFilename}...`);
    
    try {
      for (let i = 0; i < metadata.totalChunks; i++) {
        const chunkPath = path.join(this.chunksDir, transferId, `chunk-${i}.part`);
        
        if (!await fs.pathExists(chunkPath)) {
          throw new Error(`Missing chunk ${i} at ${chunkPath}`);
        }
        
        const chunkData = await fs.readFile(chunkPath);
        writeStream.write(chunkData);
      }
      
      writeStream.end();
      
      // Wait for write stream to finish
      await new Promise<void>((resolve, reject) => {
        writeStream.on('finish', () => resolve());
        writeStream.on('error', reject);
      });
      
      // Verify file size
      const stats = await fs.stat(outputPath);
      if (stats.size !== metadata.totalSize) {
        throw new Error(`Size mismatch: expected ${metadata.totalSize}, got ${stats.size}`);
      }
      
      console.log(`✅ File reassembled successfully: ${outputPath} (${(stats.size / 1024 / 1024).toFixed(2)}MB)`);
      
      return outputPath;
      
    } catch (error) {
      // Clean up partial file on error
      if (await fs.pathExists(outputPath)) {
        await fs.unlink(outputPath);
      }
      throw error;
    }
  }

  public async cleanupTransfer(transferId: string): Promise<void> {
    try {
      // Remove chunks directory
      const transferChunksDir = path.join(this.chunksDir, transferId);
      if (await fs.pathExists(transferChunksDir)) {
        await fs.remove(transferChunksDir);
      }
      
      // Remove metadata
      const metadataPath = path.join(this.metadataDir, `${transferId}.json`);
      if (await fs.pathExists(metadataPath)) {
        await fs.unlink(metadataPath);
      }
      
      // Remove from memory
      this.chunkMetadata.delete(transferId);
      
      console.log(`🧹 Cleaned up transfer: ${transferId}`);
      
    } catch (error) {
      console.error(`Failed to cleanup transfer ${transferId}:`, error);
    }
  }

  public getTransferStatus(transferId: string): ChunkMetadata | null {
    return this.chunkMetadata.get(transferId) || null;
  }

  public getUploadProgress(transferId: string): { uploaded: number; total: number; percentage: number } | null {
    const metadata = this.chunkMetadata.get(transferId);
    if (!metadata) return null;
    
    const uploaded = metadata.uploadedChunks.size;
    const total = metadata.totalChunks;
    const percentage = (uploaded / total) * 100;
    
    return { uploaded, total, percentage };
  }

  private startCleanupTimer(): void {
    // Run cleanup every hour
    setInterval(() => {
      this.cleanupOldTransfers();
    }, 60 * 60 * 1000);
  }

  private async cleanupOldTransfers(): Promise<void> {
    const now = Date.now();
    const transfersToCleanup: string[] = [];
    
    for (const [transferId, metadata] of this.chunkMetadata.entries()) {
      if (now - metadata.lastActivity > ChunkManager.CLEANUP_TIMEOUT) {
        transfersToCleanup.push(transferId);
      }
    }
    
    for (const transferId of transfersToCleanup) {
      console.log(`🧹 Cleaning up old transfer: ${transferId}`);
      await this.cleanupTransfer(transferId);
    }
    
    if (transfersToCleanup.length > 0) {
      console.log(`🧹 Cleaned up ${transfersToCleanup.length} old transfers`);
    }
  }
}
