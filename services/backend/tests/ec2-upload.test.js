const request = require('supertest');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Test configuration
const TEST_SERVER_PORT = 3001;
const TEST_FILES_DIR = path.join(__dirname, 'test-files');
const BACKEND_DIR = path.join(__dirname, '..');

describe('EC2 Direct Upload Integration Tests', () => {
  let server;
  let app;

  beforeAll(async () => {
    // Create test files directory
    if (!fs.existsSync(TEST_FILES_DIR)) {
      fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
    }

    // Create test files
    await createTestFiles();

    // Start the server
    process.env.PORT = TEST_SERVER_PORT;
    process.env.NODE_ENV = 'test';
    process.env.EC2_UPLOAD_DIR = path.join(TEST_FILES_DIR, 'uploads');
    process.env.EC2_WORK_DIR = path.join(TEST_FILES_DIR, 'work');
    process.env.AWS_REGION = 'us-east-1';
    process.env.COMPRESSED_BUCKET = 'test-bucket';

    // Create upload and work directories
    fs.mkdirSync(process.env.EC2_UPLOAD_DIR, { recursive: true });
    fs.mkdirSync(process.env.EC2_WORK_DIR, { recursive: true });

    // Import and start the server
    const serverModule = require('../src/server');
    app = serverModule.app || serverModule;
    
    // Wait for server to be ready
    await new Promise(resolve => setTimeout(resolve, 2000));
  });

  afterAll(async () => {
    // Clean up test files
    if (fs.existsSync(TEST_FILES_DIR)) {
      fs.rmSync(TEST_FILES_DIR, { recursive: true, force: true });
    }

    // Close server if it exists
    if (server && server.close) {
      server.close();
    }
  });

  describe('Health Check', () => {
    test('should return health status', async () => {
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('File Upload Validation', () => {
    test('should reject upload without file', async () => {
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .expect(400);

      expect(response.body).toHaveProperty('error', 'No file uploaded');
    });

    test('should reject oversized file', async () => {
      // For this test, we'll set a small file size limit in the test environment
      // The test setup should have MAX_FILE_SIZE set to a small value
      const largePath = path.join(TEST_FILES_DIR, 'large-file.txt');
      const largeContent = 'x'.repeat(1024 * 1024); // 1MB for testing
      fs.writeFileSync(largePath, largeContent);

      // Since we can't change the multer config after server start,
      // let's test with a file that exceeds our test limit
      // We'll set MAX_FILE_SIZE=500000 (500KB) in test setup
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', largePath)
        .expect(413);

      expect(response.body).toHaveProperty('error');
    });

    test('should reject unsupported file type', async () => {
      const unsupportedPath = path.join(TEST_FILES_DIR, 'test.xyz');
      fs.writeFileSync(unsupportedPath, 'unsupported file content');

      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', unsupportedPath)
        .expect(415);

      expect(response.body).toHaveProperty('error', 'Unsupported file type');
    });
  });

  describe('File Upload and Compression', () => {
    test('should successfully upload and compress text file', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'test.txt');
      
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('transferId');
      expect(response.body).toHaveProperty('file');
      expect(response.body.file).toHaveProperty('originalFilename', 'test.txt');
      expect(response.body.file).toHaveProperty('originalSize');
      expect(response.body.file).toHaveProperty('compressedSize');
      expect(response.body.file).toHaveProperty('compressionRatio');
      expect(response.body).toHaveProperty('download');
      expect(response.body.download).toHaveProperty('url');
    }, 30000); // 30 second timeout for compression

    test('should successfully upload and compress CSV file', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'test.csv');
      
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.file).toHaveProperty('originalFilename', 'test.csv');
      expect(response.body.file.compressionRatio).toBeGreaterThan(0);
    }, 30000);

    test('should successfully upload and compress PDF file', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'test.pdf');
      
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.file).toHaveProperty('originalFilename', 'test.pdf');
    }, 30000);
  });

  describe('Analytics', () => {
    test('should return upload analytics', async () => {
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .get('/api/analytics/ec2')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('stats');
      expect(response.body.stats).toHaveProperty('totalUploads');
      expect(response.body.stats).toHaveProperty('successfulUploads');
      expect(response.body.stats).toHaveProperty('averageCompressionRatio');
    });

    test('should return analytics for custom timeframe', async () => {
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .get('/api/analytics/ec2?hours=1')
        .expect(200);

      expect(response.body).toHaveProperty('timeframe', '1 hours');
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce upload rate limits', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'small.txt');
      
      // Make multiple rapid requests
      const requests = [];
      for (let i = 0; i < 7; i++) { // Exceed the limit of 5
        requests.push(
          request(`http://localhost:${TEST_SERVER_PORT}`)
            .post('/api/upload-direct')
            .attach('file', testFile)
        );
      }

      const responses = await Promise.allSettled(requests);
      
      // Some requests should be rate limited
      const rateLimited = responses.some(result => 
        result.status === 'fulfilled' && result.value.status === 429
      );
      
      expect(rateLimited).toBe(true);
    }, 60000);
  });

  describe('Error Handling', () => {
    test('should handle compression errors gracefully', async () => {
      // Create a file that might cause compression issues
      const problematicFile = path.join(TEST_FILES_DIR, 'problematic.txt');
      fs.writeFileSync(problematicFile, ''); // Empty file

      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', problematicFile);

      // Should either succeed or fail gracefully with proper error message
      if (response.status !== 200) {
        expect(response.body).toHaveProperty('error');
        expect(typeof response.body.error).toBe('string');
      }
    });
  });

  describe('Cleanup', () => {
    test('should clean up temporary files after processing', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'cleanup-test.txt');
      
      const response = await request(`http://localhost:${TEST_SERVER_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      // Wait a bit for cleanup to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check that upload directory is clean (or has minimal files)
      const uploadFiles = fs.readdirSync(process.env.EC2_UPLOAD_DIR);
      const workFiles = fs.readdirSync(process.env.EC2_WORK_DIR);

      // Should have minimal or no leftover files
      expect(uploadFiles.length).toBeLessThanOrEqual(1);
      expect(workFiles.length).toBeLessThanOrEqual(1);
    }, 30000);
  });
});

// Helper function to create test files
async function createTestFiles() {
  const files = [
    {
      name: 'test.txt',
      content: 'This is a test text file for compression testing.\n'.repeat(100)
    },
    {
      name: 'test.csv',
      content: 'Name,Age,City\nJohn,25,New York\nJane,30,Los Angeles\n'.repeat(50)
    },
    {
      name: 'small.txt',
      content: 'Small test file'
    },
    {
      name: 'cleanup-test.txt',
      content: 'File for cleanup testing'
    }
  ];

  // Create a simple PDF-like file (not a real PDF, but with .pdf extension)
  files.push({
    name: 'test.pdf',
    content: '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n'.repeat(10)
  });

  for (const file of files) {
    const filePath = path.join(TEST_FILES_DIR, file.name);
    fs.writeFileSync(filePath, file.content);
  }
}

// Performance test helper
function measurePerformance(fn) {
  return async (...args) => {
    const start = Date.now();
    const result = await fn(...args);
    const duration = Date.now() - start;
    return { result, duration };
  };
}
