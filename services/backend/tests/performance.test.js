const request = require('supertest');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

// Performance test configuration
const PERFORMANCE_TEST_PORT = 3002;
const TEST_FILES_DIR = path.join(__dirname, 'performance-test-files');
const TARGET_COMPRESSION_TIME = 120000; // 2 minutes in milliseconds

describe('EC2 Upload Performance Tests', () => {
  let app;
  
  beforeAll(async () => {
    // Create performance test files directory
    if (!fs.existsSync(TEST_FILES_DIR)) {
      fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
    }

    // Set environment for performance testing
    process.env.PORT = PERFORMANCE_TEST_PORT;
    process.env.NODE_ENV = 'performance-test';
    process.env.EC2_UPLOAD_DIR = path.join(TEST_FILES_DIR, 'uploads');
    process.env.EC2_WORK_DIR = path.join(TEST_FILES_DIR, 'work');
    
    // Create directories
    fs.mkdirSync(process.env.EC2_UPLOAD_DIR, { recursive: true });
    fs.mkdirSync(process.env.EC2_WORK_DIR, { recursive: true });

    // Create test files
    await createPerformanceTestFiles();
  });

  afterAll(async () => {
    // Clean up performance test files
    if (fs.existsSync(TEST_FILES_DIR)) {
      fs.rmSync(TEST_FILES_DIR, { recursive: true, force: true });
    }
  });

  describe('Large File Compression Performance', () => {
    test('should compress 100MB SQL file within reasonable time', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'large-100mb.sql');
      const startTime = performance.now();
      
      const response = await request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.file).toHaveProperty('compressionRatio');
      expect(response.body.processing).toHaveProperty('totalTime');
      
      // Should complete within 30 seconds for 100MB
      expect(processingTime).toBeLessThan(30000);
      
      console.log(`100MB SQL file processed in ${processingTime.toFixed(2)}ms`);
      console.log(`Compression ratio: ${response.body.file.compressionRatio}%`);
    }, 60000);

    test('should compress 500MB SQL file within target time', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'large-500mb.sql');
      const startTime = performance.now();
      
      const response = await request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.file).toHaveProperty('compressionRatio');
      
      // Should complete within 60 seconds for 500MB
      expect(processingTime).toBeLessThan(60000);
      
      console.log(`500MB SQL file processed in ${processingTime.toFixed(2)}ms`);
      console.log(`Compression ratio: ${response.body.file.compressionRatio}%`);
    }, 120000);

    // This test is commented out as it requires a very large file
    // Uncomment for actual performance testing with 5GB files
    /*
    test('should compress 5GB SQL file within 2 minutes', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'large-5gb.sql');
      const startTime = performance.now();
      
      const response = await request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.file).toHaveProperty('compressionRatio');
      
      // Should complete within 2 minutes (120 seconds)
      expect(processingTime).toBeLessThan(TARGET_COMPRESSION_TIME);
      
      console.log(`5GB SQL file processed in ${processingTime.toFixed(2)}ms`);
      console.log(`Compression ratio: ${response.body.file.compressionRatio}%`);
    }, 180000); // 3 minute timeout
    */
  });

  describe('Concurrent Upload Performance', () => {
    test('should handle multiple concurrent uploads', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'concurrent-test.sql');
      const concurrentUploads = 3;
      const startTime = performance.now();
      
      const uploadPromises = Array(concurrentUploads).fill().map((_, index) => 
        request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
          .post('/api/upload-direct')
          .attach('file', testFile)
          .field('uploadId', `concurrent-${index}`)
      );

      const responses = await Promise.allSettled(uploadPromises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // At least some uploads should succeed
      const successfulUploads = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      );
      
      expect(successfulUploads.length).toBeGreaterThan(0);
      
      console.log(`${successfulUploads.length}/${concurrentUploads} concurrent uploads succeeded`);
      console.log(`Total time for concurrent uploads: ${totalTime.toFixed(2)}ms`);
    }, 120000);
  });

  describe('Memory Usage Performance', () => {
    test('should maintain reasonable memory usage during large file processing', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'large-100mb.sql');
      const initialMemory = process.memoryUsage();
      
      const response = await request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      expect(response.body).toHaveProperty('success', true);
      
      // Memory increase should be reasonable (less than 500MB for 100MB file)
      expect(memoryIncrease).toBeLessThan(500 * 1024 * 1024);
      
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      console.log(`Final heap usage: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    }, 60000);
  });

  describe('Compression Efficiency', () => {
    test('should achieve good compression ratios for SQL files', async () => {
      const testFile = path.join(TEST_FILES_DIR, 'large-100mb.sql');
      
      const response = await request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
        .post('/api/upload-direct')
        .attach('file', testFile)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.file).toHaveProperty('compressionRatio');
      
      // SQL files should compress well (expect at least 50% compression)
      expect(response.body.file.compressionRatio).toBeGreaterThan(50);
      
      console.log(`SQL compression ratio: ${response.body.file.compressionRatio}%`);
    }, 60000);

    test('should handle different file types efficiently', async () => {
      const fileTypes = [
        { name: 'test.csv', expectedRatio: 70 },
        { name: 'test.txt', expectedRatio: 60 },
        { name: 'test.json', expectedRatio: 80 }
      ];

      for (const fileType of fileTypes) {
        const testFile = path.join(TEST_FILES_DIR, fileType.name);
        
        const response = await request(`http://localhost:${PERFORMANCE_TEST_PORT}`)
          .post('/api/upload-direct')
          .attach('file', testFile);

        if (response.status === 200) {
          expect(response.body.file.compressionRatio).toBeGreaterThan(fileType.expectedRatio);
          console.log(`${fileType.name} compression ratio: ${response.body.file.compressionRatio}%`);
        }
      }
    }, 120000);
  });
});

// Helper function to create performance test files
async function createPerformanceTestFiles() {
  const files = [
    {
      name: 'large-100mb.sql',
      size: 100 * 1024 * 1024, // 100MB
      content: generateSQLContent
    },
    {
      name: 'large-500mb.sql',
      size: 500 * 1024 * 1024, // 500MB
      content: generateSQLContent
    },
    {
      name: 'concurrent-test.sql',
      size: 10 * 1024 * 1024, // 10MB
      content: generateSQLContent
    },
    {
      name: 'test.csv',
      size: 1024 * 1024, // 1MB
      content: generateCSVContent
    },
    {
      name: 'test.txt',
      size: 1024 * 1024, // 1MB
      content: generateTextContent
    },
    {
      name: 'test.json',
      size: 1024 * 1024, // 1MB
      content: generateJSONContent
    }
  ];

  for (const file of files) {
    const filePath = path.join(TEST_FILES_DIR, file.name);
    if (!fs.existsSync(filePath)) {
      console.log(`Creating performance test file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
      await createLargeFile(filePath, file.size, file.content);
    }
  }
}

// Content generators for different file types
function generateSQLContent(size) {
  const baseInsert = "INSERT INTO test_table (id, name, email, data, timestamp) VALUES ";
  const valueTemplate = "({{id}}, 'User {{id}}', 'user{{id}}@example.com', '{{data}}', '2024-01-01 12:00:00')";
  
  let content = "CREATE TABLE test_table (id INT, name VARCHAR(100), email VARCHAR(100), data TEXT, timestamp DATETIME);\n\n";
  let currentSize = content.length;
  let id = 1;
  
  while (currentSize < size) {
    const values = [];
    for (let i = 0; i < 1000 && currentSize < size; i++) {
      const data = 'x'.repeat(100); // 100 chars of data
      const value = valueTemplate
        .replace(/{{id}}/g, id)
        .replace(/{{data}}/g, data);
      values.push(value);
      currentSize += value.length + 2; // +2 for comma and space
      id++;
    }
    
    if (values.length > 0) {
      content += baseInsert + values.join(', ') + ';\n';
    }
  }
  
  return content.substring(0, size);
}

function generateCSVContent(size) {
  let content = "id,name,email,age,city,country,data\n";
  let currentSize = content.length;
  let id = 1;
  
  while (currentSize < size) {
    const row = `${id},User ${id},user${id}@example.com,${25 + (id % 50)},City${id % 100},Country${id % 20},${'x'.repeat(50)}\n`;
    content += row;
    currentSize += row.length;
    id++;
  }
  
  return content.substring(0, size);
}

function generateTextContent(size) {
  const paragraph = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n\n";
  
  let content = "";
  while (content.length < size) {
    content += paragraph;
  }
  
  return content.substring(0, size);
}

function generateJSONContent(size) {
  let content = '{"data": [\n';
  let currentSize = content.length;
  let id = 1;
  
  while (currentSize < size - 100) { // Leave room for closing
    const obj = {
      id: id,
      name: `User ${id}`,
      email: `user${id}@example.com`,
      data: 'x'.repeat(100),
      timestamp: '2024-01-01T12:00:00Z'
    };
    
    const objStr = JSON.stringify(obj) + (currentSize < size - 200 ? ',' : '') + '\n';
    content += objStr;
    currentSize += objStr.length;
    id++;
  }
  
  content += ']}';
  return content.substring(0, size);
}

// Helper to create large files efficiently
async function createLargeFile(filePath, targetSize, contentGenerator) {
  const content = contentGenerator(targetSize);
  fs.writeFileSync(filePath, content);
}
